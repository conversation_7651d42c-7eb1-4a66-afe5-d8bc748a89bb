@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\llm\examples

python train_candlestick_vq_gpt.py ^
--data_dir f:/hqdata/tsdb ^
--market fut ^
--block_name top ^
--period min1 ^
--begin_date 2025-04-01 ^
--end_date 2025-12-31 ^
--val_ratio 0.1 ^
--stride 1 ^
--codebook_path E:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250522\vqcb_atr_based_fut_top_min1_512_0.0202.pt ^
--num_embeddings 512 ^
--embedding_dim 4 ^
--atr_period 14 ^
--ma_volume_period 14 ^
--vectorization_method atr_based ^
--block_size 30 ^
--code_size 100 ^
--n_layer 4 ^
--n_head 8 ^
--d_model 16 ^
--dropout 0.1 ^
--use_time_features ^
--label_smoothing 0.1 ^
--use_auxiliary_loss ^
--batch_size 64 ^
--epochs 5 ^
--learning_rate 1e-3 ^
--weight_decay 0.01 ^
--warmup_ratio 0.1 ^
--grad_clip 1.0 ^
--grad_accum_steps 2 ^
--early_stopping 10 ^
--num_workers 4 ^
--save_dir e:/lab/RoboQuant/pylab/checkpoints/candlestick_vq_gpt ^
--log_interval 50 ^
--eval_interval 500 ^
--save_interval 500 ^
--seed 42
@REM --mixed_precision ^
